'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
}

type AuthMode = 'login' | 'register' | 'forgot-password'

export function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const { user, signInWithEmail, signUpWithEmail, resetPassword, error } = useAuth()
  const [mode, setMode] = useState<AuthMode>('login')
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    displayName: '',
    confirmPassword: ''
  })
  const [formError, setFormError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  // Check if user is anonymous
  const isAnonymousUser = user?.isAnonymous || false

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({ email: '', password: '', displayName: '', confirmPassword: '' })
      setFormError(null)
      setSuccessMessage(null)
      setMode('login')
    }
  }, [isOpen])

  // Clear errors when switching modes
  useEffect(() => {
    setFormError(null)
    setSuccessMessage(null)
  }, [mode])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setFormError(null)
  }

  const validateForm = (): boolean => {
    if (!formData.email.trim()) {
      setFormError('Email is required')
      return false
    }

    if (!formData.email.includes('@')) {
      setFormError('Please enter a valid email address')
      return false
    }

    if (mode === 'forgot-password') {
      return true
    }

    if (!formData.password) {
      setFormError('Password is required')
      return false
    }

    if (formData.password.length < 6) {
      setFormError('Password must be at least 6 characters long')
      return false
    }

    if (mode === 'register') {
      if (!formData.displayName.trim()) {
        setFormError('Display name is required')
        return false
      }

      if (formData.password !== formData.confirmPassword) {
        setFormError('Passwords do not match')
        return false
      }
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    setFormError(null)

    try {
      if (mode === 'login') {
        const user = await signInWithEmail(formData.email, formData.password)
        if (user) {
          if (isAnonymousUser) {
            setSuccessMessage('Account linked successfully! Your data has been saved.')
            setTimeout(() => onClose(), 2000) // Show success message briefly
          } else {
            onClose()
          }
        }
      } else if (mode === 'register') {
        const user = await signUpWithEmail(formData.email, formData.password, formData.displayName)
        if (user) {
          if (isAnonymousUser) {
            setSuccessMessage('Account created and linked successfully! Your data has been saved.')
            setTimeout(() => onClose(), 2000) // Show success message briefly
          } else {
            onClose()
          }
        }
      } else if (mode === 'forgot-password') {
        await resetPassword(formData.email)
        setSuccessMessage('Password reset email sent! Check your inbox.')
      }
    } catch (err: any) {
      setFormError(err.message || 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  const getTitle = () => {
    switch (mode) {
      case 'login': return 'Sign In'
      case 'register': return 'Create Account'
      case 'forgot-password': return 'Reset Password'
    }
  }

  const getSubmitText = () => {
    if (isLoading) {
      switch (mode) {
        case 'login': return 'Signing in...'
        case 'register': return 'Creating account...'
        case 'forgot-password': return 'Sending email...'
      }
    }
    switch (mode) {
      case 'login': return 'Sign In'
      case 'register': return 'Create Account'
      case 'forgot-password': return 'Send Reset Email'
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm overflow-y-auto">
      <div className="glassmorphism rounded-2xl p-6 w-full max-w-md min-h-fit max-h-[80vh] overflow-y-auto relative my-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">{getTitle()}</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-white/10 transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        {/* Account Linking Info for Anonymous Users */}
        {isAnonymousUser && mode !== 'forgot-password' && (
          <div className="mb-4 p-3 rounded-lg bg-blue-500/20 border border-blue-500/30 text-blue-200 text-sm">
            <div className="flex items-start space-x-2">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="mt-0.5 flex-shrink-0">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <div>
                <p className="font-medium">Save Your Data</p>
                <p className="text-xs mt-1 text-blue-300">
                  {mode === 'register'
                    ? 'Creating an account will save all your current queues and preferences permanently.'
                    : 'Signing in will link your current queues and preferences to your account.'
                  }
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error/Success Messages */}
        {(formError || error) && (
          <div className="mb-4 p-3 rounded-lg bg-red-500/20 border border-red-500/30 text-red-200 text-sm">
            {formError || error}
          </div>
        )}

        {successMessage && (
          <div className="mb-4 p-3 rounded-lg bg-green-500/20 border border-green-500/30 text-green-200 text-sm">
            {successMessage}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Display Name (Register only) */}
          {mode === 'register' && (
            <div>
              <label htmlFor="displayName" className="block text-sm font-medium text-dark-200 mb-2">
                Display Name
              </label>
              <input
                type="text"
                id="displayName"
                name="displayName"
                value={formData.displayName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Your name"
                required={mode === 'register'}
              />
            </div>
          )}

          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-dark-200 mb-2">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="<EMAIL>"
              required
            />
          </div>

          {/* Password (not for forgot password) */}
          {mode !== 'forgot-password' && (
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-dark-200 mb-2">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Password"
                required
                minLength={6}
              />
            </div>
          )}

          {/* Confirm Password (Register only) */}
          {mode === 'register' && (
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-dark-200 mb-2">
                Confirm Password
              </label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Confirm password"
                required={mode === 'register'}
              />
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {getSubmitText()}
          </button>
        </form>

        {/* Footer Links */}
        <div className="mt-6 text-center space-y-2">
          {mode === 'login' && (
            <>
              <button
                type="button"
                onClick={() => setMode('forgot-password')}
                className="text-sm text-primary-400 hover:text-primary-300 transition-colors"
              >
                Forgot your password?
              </button>
              <div className="text-sm text-dark-300">
                Don't have an account?{' '}
                <button
                  type="button"
                  onClick={() => setMode('register')}
                  className="text-primary-400 hover:text-primary-300 transition-colors"
                >
                  Sign up
                </button>
              </div>
            </>
          )}

          {mode === 'register' && (
            <div className="text-sm text-dark-300">
              Already have an account?{' '}
              <button
                type="button"
                onClick={() => setMode('login')}
                className="text-primary-400 hover:text-primary-300 transition-colors"
              >
                Sign in
              </button>
            </div>
          )}

          {mode === 'forgot-password' && (
            <div className="text-sm text-dark-300">
              Remember your password?{' '}
              <button
                type="button"
                onClick={() => setMode('login')}
                className="text-primary-400 hover:text-primary-300 transition-colors"
              >
                Sign in
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
